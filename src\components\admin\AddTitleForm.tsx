import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { But<PERSON> } from "@/components/ui/button";
import { Toggle } from "@/components/ui/toggle";
import { ChevronDown, FileUp, Star, Video, UploadCloud, Info, CheckCircle, X, Upload, Loader2, Search } from "lucide-react";
import { Label } from "@/components/ui/label";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { encodeVideoLinks, parseVideoLinks, isValidVideoLink } from "@/utils/videoSecurity";
import SecureVideoPlayer from "@/components/SecureVideoPlayer";
import {
  getComprehensiveContentData,
  getPosterUrls,
  getBackdropUrls,
  isValidTMDBId,
  TMDBError
} from "@/services/tmdbService";
import TMDBSearchDialog from "./TMDBSearchDialog";
import BulkAddMode from "./BulkAddMode";
import { MediaItem } from "@/types/media";

const GENRES = [
  "Action", "Adventure", "Comedy", "Drama", "Fantasy", "Thriller", "Horror", "Sci-Fi", "Romance", "Crime", "Mystery", "Animation", "Family"
];
const LANGUAGES = [
  "Hindi", "English", "Tamil", "Telugu", "Malayalam", "Korean", "Japanese", "Anime"
];
const QUALITY = ["HD", "WEB", "BluRay", "Cam", "HDTS", "HDTC"];

const CATEGORIES = [
  "Hindi Movies",
  "Hindi Web Series",
  "English Movies",
  "English Web Series",
  "Telugu Movies",
  "Telugu Web Series",
  "Tamil Movies",
  "Tamil Web Series",
  "Malayalam Movies",
  "Malayalam Web Series",
  "Korean Movies",
  "Korean Web Series",
  "Japanese Movies",
  "Japanese Web Series",
  "Anime",
  "Hindi Dubbed",
  "English Dubbed",
  "Animation"
];

interface FormData {
  title: string;
  type: string;
  category: string;
  tmdbId: string;
  year: string;
  genres: string[];
  languages: string[];
  description: string;
  posterUrl: string;
  thumbnailUrl: string;
  videoLinks: string;
  secureVideoLinks: string; // Encoded video links for security
  quality: string[];
  tags: string;
  imdbRating: string;
  runtime: string;
  studio: string;
  audioTracks: string[];
  trailer: string;
  subtitleFile: File | null;
  subtitleUrl: string;
  isPublished: boolean;
  isFeatured: boolean;
  addToCarousel: boolean;
}



export default function AddTitleForm() {
  const { toast } = useToast();
  const [showPosterUrl, setShowPosterUrl] = useState(false);
  const [showThumbnailUrl, setShowThumbnailUrl] = useState(false);

  const [showBulkAddMode, setShowBulkAddMode] = useState(false);
  
  const [formData, setFormData] = useState<FormData>({
    title: "",
    type: "movie",
    category: "",
    tmdbId: "",
    year: "",
    genres: [],
    languages: [],
    description: "",
    posterUrl: "",
    thumbnailUrl: "",
    videoLinks: "",
    secureVideoLinks: "",
    quality: [],
    tags: "",
    imdbRating: "",
    runtime: "",
    studio: "",
    audioTracks: [],
    trailer: "",
    subtitleFile: null,
    subtitleUrl: "",
    isPublished: false,
    isFeatured: false,
    addToCarousel: false,
  });



  const [newGenre, setNewGenre] = useState("");
  const [isFetching, setIsFetching] = useState(false);
  const [tmdbData, setTmdbData] = useState<any>(null);
  const [fetchError, setFetchError] = useState<string>("");
  const [showTMDBSearch, setShowTMDBSearch] = useState(false);

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleMultiSelect = (field: keyof FormData, value: string) => {
    const currentArray = formData[field] as string[];
    const newArray = currentArray.includes(value)
      ? currentArray.filter(item => item !== value)
      : [...currentArray, value];
    handleInputChange(field, newArray);
  };

  const handleQualityToggle = (quality: string) => {
    handleMultiSelect('quality', quality);
  };

  const handleFetchFromTMDB = async () => {
    if (!formData.tmdbId) {
      toast({
        title: "Error",
        description: "Please enter a TMDB ID first",
        variant: "destructive",
      });
      return;
    }

    if (!isValidTMDBId(formData.tmdbId)) {
      toast({
        title: "Error",
        description: "Please enter a valid TMDB ID (numbers only)",
        variant: "destructive",
      });
      return;
    }

    setIsFetching(true);
    setFetchError("");

    try {
      toast({
        title: "Fetching from TMDB",
        description: "Retrieving content data from TMDB API...",
      });

      const result = await getComprehensiveContentData(formData.tmdbId);

      if (!result.success) {
        throw new Error(result.error || "Failed to fetch data from TMDB");
      }

      const data = result.data;
      setTmdbData(result.rawData);

      // Update form with fetched data
      setFormData(prev => ({
        ...prev,
        title: data.title,
        type: result.contentType,
        year: data.year,
        description: data.description,
        genres: data.genres,
        languages: data.languages,
        posterUrl: data.posterUrl,
        thumbnailUrl: data.thumbnailUrl,
        imdbRating: data.imdbRating,
        runtime: data.runtime,
        studio: data.studio,
        trailer: data.trailer,
      }));

      toast({
        title: "Success",
        description: `${result.contentType === 'movie' ? 'Movie' : 'TV Series'} data fetched successfully from TMDB!`,
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      setFetchError(errorMessage);

      toast({
        title: "Error",
        description: `Failed to fetch from TMDB: ${errorMessage}`,
        variant: "destructive",
      });
    } finally {
      setIsFetching(false);
    }
  };

  const handleFetchPosterFromTMDB = async () => {
    if (!formData.tmdbId) {
      toast({
        title: "Error",
        description: "Please enter a TMDB ID first",
        variant: "destructive",
      });
      return;
    }

    if (!isValidTMDBId(formData.tmdbId)) {
      toast({
        title: "Error",
        description: "Please enter a valid TMDB ID (numbers only)",
        variant: "destructive",
      });
      return;
    }

    setIsFetching(true);

    try {
      toast({
        title: "Fetching Poster",
        description: "Retrieving poster from TMDB API...",
      });

      const result = await getComprehensiveContentData(formData.tmdbId);

      if (!result.success) {
        throw new Error(result.error || "Failed to fetch poster from TMDB");
      }

      if (!result.data.posterUrl) {
        throw new Error("No poster available for this content");
      }

      setFormData(prev => ({
        ...prev,
        posterUrl: result.data.posterUrl,
        thumbnailUrl: result.data.thumbnailUrl,
      }));

      toast({
        title: "Success",
        description: "Poster fetched from TMDB successfully!",
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";

      toast({
        title: "Error",
        description: `Failed to fetch poster: ${errorMessage}`,
        variant: "destructive",
      });
    } finally {
      setIsFetching(false);
    }
  };

  const handleFetchThumbnailFromTMDB = async () => {
    if (!formData.tmdbId) {
      toast({
        title: "Error",
        description: "Please enter a TMDB ID first",
        variant: "destructive",
      });
      return;
    }

    if (!isValidTMDBId(formData.tmdbId)) {
      toast({
        title: "Error",
        description: "Please enter a valid TMDB ID (numbers only)",
        variant: "destructive",
      });
      return;
    }

    setIsFetching(true);

    try {
      toast({
        title: "Fetching Thumbnail",
        description: "Retrieving thumbnail from TMDB API...",
      });

      const result = await getComprehensiveContentData(formData.tmdbId);

      if (!result.success) {
        throw new Error(result.error || "Failed to fetch thumbnail from TMDB");
      }

      if (!result.data.thumbnailUrl) {
        throw new Error("No thumbnail available for this content");
      }

      setFormData(prev => ({
        ...prev,
        thumbnailUrl: result.data.thumbnailUrl,
      }));

      toast({
        title: "Success",
        description: "Thumbnail fetched from TMDB successfully!",
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";

      toast({
        title: "Error",
        description: `Failed to fetch thumbnail: ${errorMessage}`,
        variant: "destructive",
      });
    } finally {
      setIsFetching(false);
    }
  };

  const handleFileUpload = (type: 'poster' | 'thumbnail' | 'subtitle') => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = type === 'subtitle' ? '.srt,.vtt' : 'image/*';
    
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        if (type === 'subtitle') {
          setFormData(prev => ({ ...prev, subtitleFile: file }));
          toast({
            title: "File uploaded",
            description: `Subtitle file "${file.name}" uploaded successfully`,
          });
        } else {
          // Simulate file upload and get URL
          const fakeUrl = `https://example.com/${type}/${file.name}`;
          handleInputChange(type === 'poster' ? 'posterUrl' : 'thumbnailUrl', fakeUrl);
          toast({
            title: "File uploaded",
            description: `${type} uploaded successfully`,
          });
        }
      }
    };
    
    input.click();
  };

  const addGenre = () => {
    if (newGenre.trim()) {
      setFormData(prev => ({ ...prev, genres: [...prev.genres, newGenre] }));
      setNewGenre("");
    }
  };

  const removeGenre = (index: number) => {
    setFormData(prev => ({ ...prev, genres: prev.genres.filter((_, i) => i !== index) }));
  };



  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.title || !formData.type || !formData.category) {
      toast({
        title: "Error",
        description: "Please fill in required fields (Title, Type, and Category)",
        variant: "destructive",
      });
      return;
    }

    // Ensure video links are encoded for security
    if (formData.videoLinks && !formData.secureVideoLinks) {
      const encoded = encodeVideoLinks(formData.videoLinks);
      setFormData(prev => ({ ...prev, secureVideoLinks: encoded }));
    }

    // Validate video links if provided
    if (formData.videoLinks) {
      const links = parseVideoLinks(formData.videoLinks);
      const validLinks = links.filter(link => isValidVideoLink(link));
      if (links.length > 0 && validLinks.length === 0) {
        toast({
          title: "Warning",
          description: "No valid video links found. Please check the format.",
          variant: "destructive",
        });
        return;
      }
    }

    console.log("Form Data:", formData);

    console.log("Secure Video Links:", formData.secureVideoLinks ? "✓ Encoded" : "None");

    toast({
      title: "Success",
      description: `${formData.type} "${formData.title}" saved successfully with secure video links!`,
    });

    // Reset form
    setFormData({
      title: "",
      type: "movie",
      category: "",
      tmdbId: "",
      year: "",
      genres: [],
      languages: [],
      description: "",
      posterUrl: "",
      thumbnailUrl: "",
      videoLinks: "",
      secureVideoLinks: "",
      quality: [],
      tags: "",
      imdbRating: "",
      runtime: "",
      studio: "",
      audioTracks: [],
      trailer: "",
      subtitleFile: null,
      subtitleUrl: "",
      isPublished: false,
      isFeatured: false,
      addToCarousel: false,
    });

  };

  const handleBulkAdd = () => {
    setShowBulkAddMode(true);
  };

  const handleBulkAddComplete = async (items: Partial<MediaItem>[]) => {
    // Here you would typically save the items to your database
    // For now, we'll just simulate the process
    console.log("Bulk adding items:", items);

    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    toast({
      title: "Bulk import completed",
      description: `Successfully imported ${items.length} items to your content library`,
    });
  };

  const handleCloseBulkAdd = () => {
    setShowBulkAddMode(false);
  };



  const fetchIMDbRating = async () => {
    if (!formData.tmdbId) {
      toast({
        title: "Error",
        description: "Please enter a TMDB ID first to fetch rating",
        variant: "destructive",
      });
      return;
    }

    if (!isValidTMDBId(formData.tmdbId)) {
      toast({
        title: "Error",
        description: "Please enter a valid TMDB ID (numbers only)",
        variant: "destructive",
      });
      return;
    }

    setIsFetching(true);

    try {
      toast({
        title: "Fetching Rating",
        description: "Retrieving rating from TMDB API...",
      });

      const result = await getComprehensiveContentData(formData.tmdbId);

      if (!result.success) {
        throw new Error(result.error || "Failed to fetch rating from TMDB");
      }

      if (!result.data.imdbRating) {
        throw new Error("No rating available for this content");
      }

      setFormData(prev => ({
        ...prev,
        imdbRating: result.data.imdbRating,
      }));

      toast({
        title: "Success",
        description: `Rating ${result.data.imdbRating}/10 fetched from TMDB!`,
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";

      toast({
        title: "Error",
        description: `Failed to fetch rating: ${errorMessage}`,
        variant: "destructive",
      });
    } finally {
      setIsFetching(false);
    }
  };

  const handleTMDBSearchSelect = async (tmdbId: string, contentType: 'movie' | 'tv') => {
    setFormData(prev => ({ ...prev, tmdbId }));

    // Automatically fetch data after selection
    try {
      const result = await getComprehensiveContentData(tmdbId, contentType);

      if (result.success) {
        const data = result.data;
        setTmdbData(result.rawData);

        setFormData(prev => ({
          ...prev,
          title: data.title,
          type: result.contentType,
          year: data.year,
          description: data.description,
          genres: data.genres,
          languages: data.languages,
          posterUrl: data.posterUrl,
          thumbnailUrl: data.thumbnailUrl,
          imdbRating: data.imdbRating,
          runtime: data.runtime,
          studio: data.studio,
          trailer: data.trailer,
        }));

        toast({
          title: "Content Loaded",
          description: `${result.contentType === 'movie' ? 'Movie' : 'TV Series'} data loaded from TMDB search!`,
        });
      }
    } catch (error) {
      console.error('Error loading selected content:', error);
    }
  };

  return (
    <div className="space-y-6">
      {/* Basic Details Section */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold text-primary">Basic Details</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="title">Title Name *</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              placeholder="Movie or Web Series name"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="type">Type *</Label>
            <Select value={formData.type} onValueChange={(value: 'movie' | 'series' | 'requested') => setFormData({ ...formData, type: value })}>
              <SelectTrigger>
                <SelectValue placeholder="Select type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="movie">Movie</SelectItem>
                <SelectItem value="series">Web Series</SelectItem>
                <SelectItem value="requested">Requested</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="category">Category *</Label>
            <Select value={formData.category} onValueChange={(value: string) => setFormData({ ...formData, category: value })}>
              <SelectTrigger>
                <SelectValue placeholder="Select category" />
              </SelectTrigger>
              <SelectContent className="max-h-60 overflow-y-auto">
                {CATEGORIES.map((category) => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="tmdb-id">TMDB ID</Label>
            <div className="flex gap-2">
              <Input
                id="tmdb-id"
                value={formData.tmdbId}
                onChange={(e) => setFormData({ ...formData, tmdbId: e.target.value })}
                placeholder="e.g. 123456"
                className="flex-1"
              />
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowTMDBSearch(true)}
                className="shrink-0 text-xs sm:text-sm px-2 sm:px-4"
              >
                <Search className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
                <span className="hidden sm:inline">Search</span>
                <span className="sm:hidden">🔍</span>
              </Button>
              <Button
                type="button"
                variant="secondary"
                onClick={handleFetchFromTMDB}
                disabled={!formData.tmdbId || isFetching}
                className="shrink-0 text-xs sm:text-sm px-2 sm:px-4"
              >
                {isFetching ? (
                  <>
                    <Loader2 className="w-3 h-3 sm:w-4 sm:h-4 mr-1 animate-spin" />
                    <span className="hidden sm:inline">Fetching...</span>
                    <span className="sm:hidden">...</span>
                  </>
                ) : (
                  <>
                    <span className="hidden sm:inline">Fetch Data</span>
                    <span className="sm:hidden">Fetch</span>
                  </>
                )}
              </Button>
            </div>
            <p className="text-xs text-muted-foreground">Search TMDB or enter ID manually to auto-fetch details</p>
            {fetchError && (
              <p className="text-xs text-red-500 mt-1">Error: {fetchError}</p>
            )}
            {tmdbData && (
              <div className="text-xs text-green-600 mt-1 flex items-center gap-1">
                <CheckCircle className="w-3 h-3" />
                Data fetched successfully from TMDB
              </div>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="year">Year of Release</Label>
            <Input
              id="year"
              value={formData.year}
              onChange={(e) => setFormData({ ...formData, year: e.target.value })}
              placeholder="YYYY"
              maxLength={4}
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="genres">Genres</Label>
          <div className="flex flex-wrap gap-2 mb-2">
            {formData.genres.map((genre, index) => (
              <span key={index} className="bg-secondary text-secondary-foreground px-2 py-1 rounded-md text-sm flex items-center gap-1">
                {genre}
                <button
                  type="button"
                  onClick={() => removeGenre(index)}
                  className="ml-1 hover:text-destructive"
                >
                  ×
                </button>
              </span>
            ))}
          </div>
          <div className="flex gap-2">
            <Input
              value={newGenre}
              onChange={(e) => setNewGenre(e.target.value)}
              placeholder="Add genre"
              onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addGenre())}
              className="flex-1"
            />
            <Button type="button" variant="outline" onClick={addGenre} className="shrink-0">
              Add
            </Button>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            value={formData.description}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            placeholder="Enter a description..."
            rows={4}
          />
        </div>

        {/* TMDB Data Preview */}
        {tmdbData && (
          <div className="mt-6 p-4 border border-green-200 rounded-lg bg-green-50 dark:bg-green-950 dark:border-green-800">
            <h4 className="text-sm font-semibold text-green-800 dark:text-green-200 mb-2">
              TMDB Data Retrieved Successfully
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs">
              <div>
                <span className="font-medium">Title:</span> {tmdbData.title || tmdbData.name}
              </div>
              <div>
                <span className="font-medium">Type:</span> {'title' in tmdbData ? 'Movie' : 'TV Series'}
              </div>
              <div>
                <span className="font-medium">Rating:</span> {tmdbData.vote_average?.toFixed(1)}/10
              </div>
              <div>
                <span className="font-medium">Release:</span> {tmdbData.release_date || tmdbData.first_air_date}
              </div>
              {tmdbData.genres && (
                <div className="md:col-span-2">
                  <span className="font-medium">Genres:</span> {tmdbData.genres.map((g: any) => g.name).join(', ')}
                </div>
              )}
              {tmdbData.production_companies && tmdbData.production_companies.length > 0 && (
                <div className="md:col-span-2">
                  <span className="font-medium">Studio:</span> {tmdbData.production_companies[0].name}
                </div>
              )}
              {tmdbData.credits?.cast && tmdbData.credits.cast.length > 0 && (
                <div className="md:col-span-2">
                  <span className="font-medium">Cast:</span> {tmdbData.credits.cast.slice(0, 5).map((c: any) => c.name).join(', ')}
                </div>
              )}
            </div>
          </div>
        )}
      </section>

      {/* Poster & Thumbnails Section */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold text-primary">Poster & Thumbnails</h2>
        
        <div className="space-y-4">
          <div>
            <Label>Upload Poster</Label>
            <div className="flex flex-col sm:flex-row gap-2 mt-2">
              <Button type="button" variant="outline" className="flex items-center gap-2">
                <Upload className="w-4 h-4" />
                Upload
              </Button>
              <span className="text-sm text-muted-foreground self-center">or Paste URL</span>
              <Input
                value={formData.posterUrl}
                onChange={(e) => setFormData({ ...formData, posterUrl: e.target.value })}
                placeholder="https://image-url.com/poster.jpg"
                className="flex-1"
              />
            </div>
            <Button 
              type="button" 
              variant="link" 
              onClick={handleFetchPosterFromTMDB}
              disabled={!formData.tmdbId}
              className="mt-1 p-0 h-auto text-xs"
            >
              🎬 Fetch from TMDB
            </Button>
          </div>

          <div>
            <Label>Upload Thumbnail (optional)</Label>
            <div className="flex flex-col sm:flex-row gap-2 mt-2">
              <Button type="button" variant="outline" className="flex items-center gap-2">
                <Upload className="w-4 h-4" />
                Upload
              </Button>
              <span className="text-sm text-muted-foreground self-center">or Paste URL</span>
              <Input
                value={formData.thumbnailUrl}
                onChange={(e) => setFormData({ ...formData, thumbnailUrl: e.target.value })}
                placeholder="https://image-url.com/thumbnail.jpg"
                className="flex-1"
              />
            </div>
            <div className="mt-1">
              <Button 
                type="button" 
                variant="link" 
                onClick={handleFetchThumbnailFromTMDB}
                disabled={!formData.tmdbId}
                className="p-0 h-auto text-xs break-all"
              >
                🎬 Fetch from TMDB
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Streaming Information Section */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold text-primary">Streaming Information</h2>
        
        <div className="space-y-4">
          <div>
            <Label>Add Video Embed Link(s)</Label>
            <Textarea
              placeholder="Paste embed iframe, e.g. //player.com/embed/..."
              className="mt-1 min-h-[80px]"
              value={formData.videoLinks}
              onChange={(e) => {
                const value = e.target.value;
                handleInputChange('videoLinks', value);
                // Auto-encode for security when links are added
                if (value.trim()) {
                  const encoded = encodeVideoLinks(value);
                  handleInputChange('secureVideoLinks', encoded);
                } else {
                  handleInputChange('secureVideoLinks', '');
                }
              }}
            />
            <div className="flex items-center justify-between mt-1">
              <span className="text-xs text-muted-foreground">Supports multiple links (add one per line)</span>
              {formData.videoLinks && (
                <div className="flex items-center gap-2">
                  {parseVideoLinks(formData.videoLinks).filter(link => isValidVideoLink(link)).length > 0 && (
                    <span className="text-xs text-green-600 flex items-center gap-1">
                      <CheckCircle className="w-3 h-3" />
                      {parseVideoLinks(formData.videoLinks).filter(link => isValidVideoLink(link)).length} valid link(s)
                    </span>
                  )}
                  {formData.secureVideoLinks && (
                    <span className="text-xs text-blue-600 flex items-center gap-1">
                      🔒 Secured
                    </span>
                  )}
                </div>
              )}
            </div>
          </div>
          <div>
            <Label>Preview Player</Label>
            {formData.secureVideoLinks ? (
              <SecureVideoPlayer
                encodedVideoLinks={formData.secureVideoLinks}
                title={formData.title || "Preview"}
                className="mt-2 max-w-2xl"
                showPlayerSelection={true}
              />
            ) : (
              <div className="rounded-lg bg-background border border-border shadow-inner mt-2 max-w-2xl">
                <div className="w-full aspect-video flex items-center justify-center text-muted-foreground">
                  <div className="text-center">
                    <Video className="w-12 h-12 mx-auto mb-2 opacity-50" />
                    <p>Add video links above to preview player</p>
                  </div>
                </div>
              </div>
            )}
          </div>
          <div>
            <Label>Video Quality</Label>
            <div className="flex gap-2 flex-wrap mt-2">
              {QUALITY.map(q => (
                <Toggle 
                  key={q}
                  variant="outline" 
                  size="sm"
                  pressed={formData.quality.includes(q)}
                  onPressedChange={() => handleQualityToggle(q)}
                >
                  {q}
                </Toggle>
              ))}
            </div>
            {formData.quality.length > 0 && (
              <div className="text-sm text-muted-foreground mt-2">
                Selected: {formData.quality.join(', ')}
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Metadata & Tags Section */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold text-primary">Metadata & Tags</h2>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <Label>Tags / Keywords</Label>
            <Input 
              placeholder="Separate with commas, e.g. action, hero, comedy" 
              className="mt-1"
              value={formData.tags}
              onChange={(e) => handleInputChange('tags', e.target.value)}
            />
          </div>
          <div>
            <Label>IMDb Rating</Label>
            <div className="flex items-center gap-2 mt-1">
              <Input 
                placeholder="e.g. 8.1" 
                className="w-32"
                value={formData.imdbRating}
                onChange={(e) => handleInputChange('imdbRating', e.target.value)}
              />
              <Button type="button" variant="outline" size="sm" onClick={fetchIMDbRating}>
                Fetch
              </Button>
            </div>
          </div>
          <div>
            <Label>Runtime (minutes)</Label>
            <Input 
              placeholder="e.g. 164" 
              className="mt-1"
              value={formData.runtime}
              onChange={(e) => handleInputChange('runtime', e.target.value)}
            />
          </div>
          <div>
            <Label>Studio / Production</Label>
            <Input 
              placeholder="Disney, Warner Bros, etc." 
              className="mt-1"
              value={formData.studio}
              onChange={(e) => handleInputChange('studio', e.target.value)}
            />
          </div>
        </div>
      </section>

      {/* Additional Features Section */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold text-primary">Additional Features</h2>
        
        <div className="space-y-4">

          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <div>
              <Label>Upload Subtitle (SRT / VTT)</Label>
              <Button 
                type="button" 
                variant="outline" 
                size="sm" 
                className="flex items-center gap-2 mt-2"
                onClick={() => handleFileUpload('subtitle')}
              >
                <UploadCloud className="h-4 w-4" />
                Upload File
              </Button>
              {formData.subtitleFile && (
                <div className="text-sm text-muted-foreground mt-2">
                  Uploaded: {formData.subtitleFile.name}
                </div>
              )}
            </div>
            <div>
              <Label>Paste Subtitle Link (URL)</Label>
              <Input 
                className="mt-2" 
                placeholder="Paste URL..."
                value={formData.subtitleUrl}
                onChange={(e) => handleInputChange('subtitleUrl', e.target.value)}
              />
            </div>
          </div>
          
          <div>
            <Label>Audio Tracks (Language)</Label>
            <div className="mt-2 border border-border rounded-md p-2 bg-background max-h-[60px] overflow-y-auto">
              {LANGUAGES.map(language => (
                <label
                  key={language}
                  className="flex items-center gap-2 text-sm cursor-pointer hover:bg-accent hover:text-accent-foreground focus-within:bg-accent focus-within:text-accent-foreground p-1 rounded transition-colors duration-150"
                >
                  <input
                    type="checkbox"
                    checked={formData.audioTracks.includes(language)}
                    onChange={() => handleMultiSelect('audioTracks', language)}
                    className="rounded border-border bg-background text-primary focus:ring-primary focus:ring-2 focus:ring-offset-0"
                  />
                  <span className="select-none">{language}</span>
                </label>
              ))}
            </div>
          </div>
          
          <div>
            <Label>Add Trailer</Label>
            <Input 
              placeholder="YouTube link or paste TMDB trailer URL" 
              className="mt-2"
              value={formData.trailer}
              onChange={(e) => handleInputChange('trailer', e.target.value)}
            />
          </div>
        </div>
      </section>

      {/* Admin Controls Section */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold text-primary">Admin Controls</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="flex items-center justify-between p-4 border border-border rounded-lg bg-background/50">
            <div>
              <Label htmlFor="publish" className="text-base font-medium">Publish</Label>
              <p className="text-sm text-muted-foreground">Make content visible to users</p>
            </div>
            <Switch 
              id="publish" 
              checked={formData.isPublished} 
              onCheckedChange={(checked) => handleInputChange('isPublished', checked)} 
            />
          </div>
          <div className="flex items-center justify-between p-4 border border-border rounded-lg bg-background/50">
            <div>
              <Label htmlFor="featured" className="text-base font-medium">Featured</Label>
              <p className="text-sm text-muted-foreground">Show in featured section</p>
            </div>
            <Switch 
              id="featured" 
              checked={formData.isFeatured} 
              onCheckedChange={(checked) => handleInputChange('isFeatured', checked)} 
            />
          </div>
          <div className="flex items-center justify-between p-4 border border-border rounded-lg bg-background/50">
            <div>
              <Label htmlFor="home-carousel" className="text-base font-medium">Home Carousel</Label>
              <p className="text-sm text-muted-foreground">Add to homepage slider</p>
            </div>
            <Switch 
              id="home-carousel" 
              checked={formData.addToCarousel} 
              onCheckedChange={(checked) => handleInputChange('addToCarousel', checked)} 
            />
          </div>
        </div>
      </section>

      {/* Bulk Add Mode Section */}
      {showBulkAddMode && (
        <BulkAddMode
          onBulkAdd={handleBulkAddComplete}
          onClose={handleCloseBulkAdd}
        />
      )}

      <form onSubmit={handleSubmit}>
        <div className="flex gap-4 pt-4 items-center flex-wrap">
          <Button type="submit" size="lg" className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5" />
            Save Title
          </Button>
          <Button type="button" variant="secondary" onClick={handleBulkAdd}>
            Bulk Add Mode
          </Button>
          <span className="text-xs text-muted-foreground flex items-center gap-2">
            <Info className="h-3 w-3" /> 
            * Required fields. Other sections are optional and can be filled later.
          </span>
        </div>
      </form>

      {/* TMDB Search Dialog */}
      <TMDBSearchDialog
        isOpen={showTMDBSearch}
        onClose={() => setShowTMDBSearch(false)}
        onSelectContent={handleTMDBSearchSelect}
      />
    </div>
  );
}
