
import { Link } from "react-router-dom";
import { MediaItem } from "@/types/media";
import { scrollToTop } from "@/utils/scrollToTop";

export default function CardGrid({ items }: { items: MediaItem[] }) {
  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6">
      {items.map(item => (
        <Link
          to={`/content/${item.id}`}
          key={item.id}
          onClick={scrollToTop}
          className="card-grid-item group bg-card shadow-lg hover:scale-[1.04] transition-transform duration-200 hover:ring-2 hover:ring-primary/60 flex flex-col rounded-2xl overflow-hidden"
        >
          {/* Poster image */}
          <div className="relative w-full" style={{ aspectRatio: "2/3", background: "#191d25" }}>
            <img
              src={item.image}
              alt={item.title}
              className="w-full h-full object-cover bg-[#191d25] rounded-none"
              style={{
                aspectRatio: "2/3",
                display: "block"
              }}
              loading="lazy"
            />
          </div>
          {/* Title */}
          <span
            className="card-title"
            title={item.title}
          >
            {item.title}
          </span>
          {/* Genres & year, modern layout */}
          <div
            className="flex flex-col gap-1.5 items-center justify-center px-2 pb-2 mb-1"
            style={{
              background: "none",
              borderTop: "1px solid rgba(100,100,100,0.10)"
            }}
          >
            {/* Updated card-genres uses new tan color */}
            <span className="card-genres">
              {item.genres.join(", ")}
            </span>
            <span className="card-year">{item.year}</span>
          </div>
        </Link>
      ))}
    </div>
  );
}

