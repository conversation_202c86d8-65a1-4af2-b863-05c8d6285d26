
import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON>, <PERSON>, Rocket } from "lucide-react";

// Telegram channel link
const telegramLink = "https://t.me/thestreamdb";

// Feature icons, each filled with its respective color
const features = [
  {
    icon: (
      <span className="flex items-center justify-center mr-2">
        <Bell
          size={22}
          stroke="#22c55e"
          fill="#22c55e"
          strokeWidth={2}
          aria-label="Latest Uploads"
          className="drop-shadow"
        />
      </span>
    ),
    text: "Latest Uploads",
  },
  {
    icon: (
      <span className="flex items-center justify-center mr-2">
        <Star
          size={22}
          stroke="#facc15"
          fill="#facc15"
          strokeWidth={2}
          aria-label="Exclusive Content"
          className="drop-shadow"
        />
      </span>
    ),
    text: "Exclusive Content",
  },
  {
    icon: (
      <span className="flex items-center justify-center mr-2">
        <Rocket
          size={22}
          stroke="#f43f5e"
          fill="#f43f5e"
          strokeWidth={2}
          aria-label="Early Access"
          className="drop-shadow"
        />
      </span>
    ),
    text: "Early Access",
  },
];

export default function TelegramBanner() {
  return (
    <section
      className="mb-4 mx-auto max-w-3xl"
      style={{
        background: "none",
        boxShadow: "none",
        border: "none",
        padding: 0,
        transform: "scale(1)", // Restore original size
      }}
    >
      {/* Dynamic background gradient animation (minimal & subtle) */}
      <div
        className="relative rounded-xl w-full mx-auto overflow-hidden"
        style={{ minHeight: 120 }}
      >
        <div
          className="absolute inset-0 animate-gradient-bg z-0"
          style={{
            background: "linear-gradient(120deg, #30c3fc 20%, #2a7cf7 100%)",
            backgroundSize: "200% 200%",
            zIndex: 0,
            filter: "blur(0.5px)",
          }}
        ></div>
        <div
          className="relative z-10 px-2 py-7 flex flex-col items-center justify-center"
          style={{
            color: "white",
            minHeight: 106,
          }}
        >
          {/* Telegram Icon */}
          <span
            className="flex items-center justify-center rounded-full bg-[#fff] shadow-lg mb-2"
            style={{ width: 46, height: 46 }}
          >
            <Send size={26} stroke="#2599ea" fill="none" />
          </span>
          {/* Headline */}
          <h2
            className="font-koulen text-lg md:text-2xl mb-1 drop-shadow text-center font-bold"
            style={{
              color: "#fff",
              letterSpacing: "0.02em",
              lineHeight: 1.15,
            }}
          >
            Join Our Telegram Channel
          </h2>
          {/* Subheadline */}
          <p
            className="text-sm md:text-base font-medium mb-2 text-center"
            style={{
              color: "rgba(255,255,255,0.96)",
              maxWidth: 480,
            }}
          >
            Get instant notifications about latest uploads, exclusive content, and early access to new releases!
          </p>
          {/* Features in a row, centered, icons before text */}
          <div className="flex gap-7 flex-wrap items-center justify-center mb-3">
            {features.map(feat => (
              <span
                className="flex items-center text-base font-semibold rounded-lg px-2 py-1"
                key={feat.text}
                style={{
                  background: "rgba(255,255,255,0.08)",
                  color: "#fff",
                  textShadow: "0 1px 2px #0091ff60",
                  fontSize: "1.15rem",
                }}
              >
                {feat.icon}
                <span className="ml-0.5">{feat.text}</span>
              </span>
            ))}
          </div>
          {/* Join Now Button */}
          <a
            href={telegramLink}
            target="_blank"
            rel="noopener noreferrer"
            className="mt-0.5 group"
          >
            <Button
              variant="secondary"
              className="text-xs font-bold px-5 py-2.5 rounded-full shadow-xl bg-white/80 text-[#1a67b3] border border-white/60 flex items-center gap-2 transition-colors"
              style={{
                boxShadow: "0 0.5px 10px #1da1f255, 0 1px 2px #0001",
                transition: "background 0.18s, color 0.18s"
              }}
            >
              <Send size={16} className="mr-1" />
              Join Now
            </Button>
            <style>{`
              .group:hover button, .group:focus button {
                background: #e6cb8e !important;
                color: #232323 !important;
                border-color: #e6cb8e !important;
              }
            `}</style>
          </a>
        </div>
      </div>
      <style>{`
        @keyframes gradientMove {
          0% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
          100% { background-position: 0% 50%; }
        }
        .animate-gradient-bg {
          animation: gradientMove 7s ease-in-out infinite;
        }
      `}</style>
    </section>
  );
}
