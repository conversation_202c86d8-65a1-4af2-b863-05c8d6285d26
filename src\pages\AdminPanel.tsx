
import { useState, useEffect } from "react";
import AddTitleForm from "@/components/admin/AddTitleForm";
import ContentManager from "@/components/admin/ContentManager";
import EnhancedContentManager from "@/components/admin/EnhancedContentManager";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ArrowLeft, Plus, Settings, ArrowUp, Loader2 } from "lucide-react";
import { Link } from "react-router-dom";
import { scrollToTop } from "@/utils/scrollToTop";
import { toast } from "@/components/ui/sonner";

export default function AdminPanel() {
  const [showBackToTop, setShowBackToTop] = useState(false);
  const [isRunningDiagnostics, setIsRunningDiagnostics] = useState(false);
  const [isTestingTMDB, setIsTestingTMDB] = useState(false);
  const [isVerifyingTMDB, setIsVerifyingTMDB] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setShowBackToTop(window.scrollY > 300);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleRunDiagnostics = async () => {
    setIsRunningDiagnostics(true);
    toast("🧪 Running admin panel diagnostics...", {
      description: "This may take a few seconds"
    });

    try {
      const { runAllAdminTests } = await import('@/utils/adminTestUtils');
      const results = runAllAdminTests();

      console.log('Admin Panel Test Results:', results);

      toast("✅ Diagnostics completed successfully!", {
        description: "Check the browser console for detailed results"
      });
    } catch (error) {
      console.error('Diagnostics failed:', error);
      toast("❌ Diagnostics failed", {
        description: error instanceof Error ? error.message : "Unknown error occurred"
      });
    } finally {
      setIsRunningDiagnostics(false);
    }
  };

  const handleTestTMDB = async () => {
    setIsTestingTMDB(true);
    toast("🎬 Testing TMDB API integration...", {
      description: "Running comprehensive TMDB tests"
    });

    try {
      const { runComprehensiveTMDBTests } = await import('@/utils/tmdbTestUtils');
      const results = await runComprehensiveTMDBTests();

      console.log('TMDB Integration Test Results:', results);

      const successRate = results.summary.successRate;
      if (successRate === 100) {
        toast("🎉 TMDB API tests passed!", {
          description: `All ${results.summary.totalTests} tests completed successfully`
        });
      } else if (successRate >= 80) {
        toast("⚠️ TMDB API mostly working", {
          description: `${results.summary.passedTests}/${results.summary.totalTests} tests passed (${successRate}%)`
        });
      } else {
        toast("❌ TMDB API issues detected", {
          description: `Only ${results.summary.passedTests}/${results.summary.totalTests} tests passed (${successRate}%)`
        });
      }
    } catch (error) {
      console.error('TMDB tests failed:', error);
      toast("❌ TMDB API tests failed", {
        description: error instanceof Error ? error.message : "Unknown error occurred"
      });
    } finally {
      setIsTestingTMDB(false);
    }
  };

  const handleVerifyTMDB = async () => {
    setIsVerifyingTMDB(true);
    toast("🔍 Verifying TMDB integration...", {
      description: "Quick verification of TMDB setup"
    });

    try {
      const { verifyTMDBIntegration } = await import('@/utils/tmdbVerification');
      const results = await verifyTMDBIntegration();

      console.log('TMDB Verification Results:', results);

      const successRate = results.summary.successRate;
      if (successRate === 100) {
        toast("🎉 TMDB integration verified!", {
          description: "All verification checks passed"
        });
      } else if (successRate >= 80) {
        toast("✅ TMDB integration mostly working", {
          description: `${results.summary.passedTests}/${results.summary.totalTests} checks passed`
        });
      } else {
        toast("⚠️ TMDB integration issues", {
          description: `${results.summary.errors.length} issues found. Check console for details.`
        });
      }
    } catch (error) {
      console.error('TMDB verification failed:', error);
      toast("❌ TMDB verification failed", {
        description: error instanceof Error ? error.message : "Unknown error occurred"
      });
    } finally {
      setIsVerifyingTMDB(false);
    }
  };

  return (
    <div className="min-h-screen bg-background text-foreground flex flex-col">
      <Header />
      
      <main className="flex-1 p-0 bg-background/95 text-foreground flex flex-col items-center w-full">
        <div className="w-full max-w-7xl mx-auto pt-6 sm:pt-10 pb-12 px-4 relative">
          {/* Back to Home Button - Responsive positioning */}
          <div className="absolute top-2 right-2 sm:top-4 sm:right-4 z-10">
            <Link to="/" onClick={scrollToTop}>
              <Button variant="outline" size="sm" className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm px-2 sm:px-4 py-1 sm:py-2">
                <ArrowLeft className="w-3 h-3 sm:w-4 sm:h-4" />
                <span className="hidden xs:inline">Back to Home</span>
                <span className="xs:hidden">Back</span>
              </Button>
            </Link>
          </div>

          {/* Back to Top Button - Fixed position at bottom right */}
          {showBackToTop && (
            <div className="fixed bottom-2 right-2 sm:bottom-4 sm:right-4 z-10">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={scrollToTop}
                className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm px-2 sm:px-4 py-1 sm:py-2 shadow-lg hover:shadow-xl transition-all duration-200"
              >
                <ArrowUp className="w-3 h-3 sm:w-4 sm:h-4" />
                <span className="hidden xs:inline">Back to Top</span>
                <span className="xs:hidden">Top</span>
              </Button>
            </div>
          )}
          
          <div className="pt-12 sm:pt-0">
            <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-2 text-primary text-center">Admin Panel</h1>
            <p className="text-muted-foreground mb-4 sm:mb-6 text-center max-w-xl mx-auto text-sm sm:text-base px-4">
              Manage movie & series entries directly or via TMDB. Add, edit, and preview new content to keep your streaming catalog up to date.
            </p>

            <div className="flex justify-center gap-3 mb-6 sm:mb-8 flex-wrap">
              <Link
                to="/admin/player-test"
                className="inline-flex items-center gap-2 px-4 py-2 text-sm bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors"
              >
                🎬 Test Video Player
              </Link>
              <button
                onClick={handleRunDiagnostics}
                disabled={isRunningDiagnostics}
                className="inline-flex items-center gap-2 px-4 py-2 text-sm bg-green-600 hover:bg-green-700 disabled:bg-green-400 disabled:cursor-not-allowed text-white rounded-md transition-colors"
              >
                {isRunningDiagnostics ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  "🧪"
                )}
                Run Diagnostics
              </button>
              <button
                onClick={handleTestTMDB}
                disabled={isTestingTMDB}
                className="inline-flex items-center gap-2 px-4 py-2 text-sm bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed text-white rounded-md transition-colors"
              >
                {isTestingTMDB ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  "🎬"
                )}
                Test TMDB API
              </button>
              <button
                onClick={handleVerifyTMDB}
                disabled={isVerifyingTMDB}
                className="inline-flex items-center gap-2 px-4 py-2 text-sm bg-purple-600 hover:bg-purple-700 disabled:bg-purple-400 disabled:cursor-not-allowed text-white rounded-md transition-colors"
              >
                {isVerifyingTMDB ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  "🔍"
                )}
                Verify TMDB
              </button>
            </div>

            <Tabs defaultValue="add-content" className="w-full">
              <TabsList className="grid w-full grid-cols-2 mb-6 sm:mb-8">
                <TabsTrigger value="add-content" className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm">
                  <Plus className="w-3 h-3 sm:w-4 sm:h-4" />
                  <span className="hidden sm:inline">Add New Content</span>
                  <span className="sm:hidden">Add New</span>
                </TabsTrigger>
                <TabsTrigger value="manage-content" className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm">
                  <Settings className="w-3 h-3 sm:w-4 sm:h-4" />
                  <span className="hidden sm:inline">Manage Existing Content</span>
                  <span className="sm:hidden">Manage</span>
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="add-content" className="space-y-6">
                <AddTitleForm />
              </TabsContent>
              
              <TabsContent value="manage-content" className="space-y-6">
                <EnhancedContentManager />
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
